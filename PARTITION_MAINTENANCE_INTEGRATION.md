# 分区维护任务集成到Scheduler

## 概述

已成功将数据库分区维护任务集成到weather scheduler中，实现统一的任务调度管理。

## 主要变更

### 1. 配置文件更新

#### `src/config.yml` 和 `config.yml`
```yaml
scheduler:
  tasks:
    # 分区维护任务配置
    partition_maintenance:
      timeout_minutes: 10
      schedule_pattern: "0 2 * * *"  # 每天凌晨2点执行
      description: "数据库分区维护任务"
```

#### `src/config.py`
```python
# 分区维护任务配置
SCHEDULER_PARTITION_MAINTENANCE_TASKS = SCHEDULER_TASKS.get("partition_maintenance", {})
SCHEDULER_PARTITION_MAINTENANCE_TIMEOUT_MINUTES = SCHEDULER_PARTITION_MAINTENANCE_TASKS.get("timeout_minutes", 10)
SCHEDULER_PARTITION_MAINTENANCE_SCHEDULE_PATTERN = SCHEDULER_PARTITION_MAINTENANCE_TASKS.get("schedule_pattern", "0 2 * * *")
```

### 2. Scheduler增强

#### `src/scheduler.py`
添加了以下功能：

1. **分区维护调度配置**：
   - 解析分区维护调度模式
   - 计算下一个执行时间

2. **分区维护任务执行**：
   ```python
   async def _execute_partition_maintenance_task(self):
       """执行分区维护任务"""
       # 创建数据库连接
       engine = create_engine(PG_URL)
       with engine.begin() as conn:
           conn.execute(text("CALL partman.run_maintenance_proc()"))
   ```

3. **分区维护任务调度**：
   ```python
   async def _schedule_partition_maintenance_tasks(self):
       """调度分区维护任务"""
       # 按配置的时间调度执行
   ```

4. **状态监控**：
   - 在`get_status()`中添加下一个分区维护时间

### 3. 建表脚本更新

#### `src/create_forecast_tables.py`
更新了分区维护说明：
```
📋 定时维护任务设置说明:
   分区维护任务已集成到scheduler中，无需手动设置
   scheduler会在每天凌晨2点自动执行分区维护
   
   配置位置: config.yml -> scheduler.tasks.partition_maintenance
   默认调度: 每天凌晨2点 (0 2 * * *)
```

## 功能特性

### 1. 自动调度
- **默认时间**：每天凌晨2点执行
- **可配置**：通过config.yml修改调度时间
- **Cron格式**：支持标准cron表达式

### 2. 统一管理
- 与其他天气任务统一调度
- 共享资源池和连接池
- 统一的日志记录和错误处理

### 3. 状态监控
- 通过scheduler状态API查看下一个执行时间
- 任务执行状态跟踪
- 错误日志记录

### 4. 容错处理
- 数据库连接失败自动重试
- 执行失败不影响其他任务
- 详细的错误日志

## 测试验证

已通过完整测试验证：

```
✓ 分区维护超时时间: 10 分钟
✓ 分区维护调度模式: 0 2 * * *
✓ 下一个分区维护调度时间: 2025-08-02 02:00:00
✓ 分区维护调度配置: {'minutes': [0], 'hours': [2]}
✓ 数据库连接成功
✓ pg_partman扩展已安装
```

## 使用方法

### 1. 启动Scheduler
```bash
python run_weather_scheduler.py
```

### 2. 查看状态
访问Web界面或API：
```
http://localhost:8001/status
```

### 3. 修改调度时间
编辑`config.yml`：
```yaml
scheduler:
  tasks:
    partition_maintenance:
      schedule_pattern: "0 3 * * *"  # 改为凌晨3点
```

### 4. 手动执行（如需要）
```sql
CALL partman.run_maintenance_proc();
```

## 优势

1. **统一管理**：所有定时任务在一个地方管理
2. **配置简单**：通过YAML文件配置，无需系统级定时任务
3. **监控完善**：与其他任务统一监控和日志
4. **部署简单**：无需配置crontab或Windows任务计划
5. **跨平台**：Windows和Linux统一实现

## 注意事项

1. **依赖pg_partman**：确保数据库已安装pg_partman扩展
2. **权限要求**：数据库用户需要执行存储过程的权限
3. **时区设置**：调度时间基于系统本地时区
4. **资源占用**：分区维护可能消耗一定数据库资源，建议在低峰时段执行

## 总结

分区维护任务已成功集成到scheduler中，实现了：
- 自动化的分区维护
- 统一的任务调度管理
- 完善的监控和日志
- 简化的部署和配置

无需再手动设置系统级定时任务，scheduler会自动处理所有分区维护工作。
