#!/usr/bin/env python3
# coding: utf-8
"""
WeatherApi 项目统一配置文件
包含数据库连接、文件路径、格网参数等所有共用配置

支持从以下来源加载配置（优先级从高到低）：
1. 环境变量
2. config.yml 文件
3. 代码中的默认值
"""

import os
import yaml
from urllib.parse import quote_plus
from pathlib import Path

# ================================
# 基础路径配置
# ================================

# 获取脚本文件所在的目录 (例如 /data/script/weather_script/src)
# Path(__file__) -> 获取当前脚本的绝对路径
# .parent -> 获取其父目录 (src目录)
# .parent -> 再获取父目录 (项目根目录)
SCRIPT_DIR = Path(__file__).parent.parent

# ================================
# 配置加载函数
# ================================

def load_yaml_config():
    """从YAML文件加载配置"""
    # 首先尝试从项目根目录加载
    config_file = SCRIPT_DIR / "config.yml"
    if not config_file.exists():
        # 如果根目录没有，尝试从src目录加载
        config_file = Path(__file__).parent / "config.yml"

    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"警告: 无法加载配置文件 {config_file}: {e}")
    return {}

def resolve_path(path_str):
    """解析路径，将相对路径转换为基于脚本目录的绝对路径"""
    if not path_str:
        return path_str

    path = Path(path_str)

    # 如果已经是绝对路径，直接返回
    if path.is_absolute():
        return str(path)

    # 如果是相对路径，基于脚本目录解析
    return str(SCRIPT_DIR / path)

# 加载YAML配置
_yaml_config = load_yaml_config()

# ================================
# 数据库配置
# ================================

# PostgreSQL / PostGIS 配置
POSTGRES_CONFIG = _yaml_config.get("database", {}).get("postgres", {})
if not POSTGRES_CONFIG:
    # 默认配置
    POSTGRES_CONFIG = {
        "host": "***************",
        "port": 5432,
        "database": "middle-data-dev",
        "username": "root",
        "password": "Ylzx@9008*12-3*",
        "schema": "public"
    }

# MySQL 配置
MYSQL_CONFIG = _yaml_config.get("database", {}).get("mysql", {})
if not MYSQL_CONFIG:
    # 默认配置
    MYSQL_CONFIG = {
        "host": "***************",
        "port": 13306,
        "username": "root",
        "password": "Ylzx@2000+!#-2",
        "database": "ylzx-system-dev"
    }

# ================================
# 数据库连接字符串生成
# ================================

def get_postgres_url():
    """生成PostgreSQL连接字符串"""
    return (f"postgresql://{POSTGRES_CONFIG['username']}:"
            f"{quote_plus(POSTGRES_CONFIG['password'])}@"
            f"{POSTGRES_CONFIG['host']}:{POSTGRES_CONFIG['port']}/"
            f"{POSTGRES_CONFIG['database']}")

def get_mysql_url():
    """生成MySQL连接字符串"""
    return (f"mysql+pymysql://{MYSQL_CONFIG['username']}:"
            f"{quote_plus(MYSQL_CONFIG['password'])}@"
            f"{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/"
            f"{MYSQL_CONFIG['database']}")

# ================================
# 文件路径配置
# ================================

# 数据目录配置
_paths_config = _yaml_config.get("paths", {})
DATA_PATHS = {
    "weather_dir": resolve_path(_paths_config.get("weather_dir", "./data/weather")),
    "weather_mpf_dir": resolve_path(_paths_config.get("weather_mpf_dir", "./data/weather/MPF")),
    "weather_backup_dir": resolve_path(_paths_config.get("weather_backup_dir", "./data/weather/backup")),
    "gis_route_shape": resolve_path(_paths_config.get("gis_route_shape", "./data/gis_route_shape/gis_route_shape.shp"))
}

# NetCDF 文件配置
_netcdf_config = _paths_config.get("netcdf", {})
NETCDF_CONFIG = {
    "default_file": resolve_path(_netcdf_config.get("default_file", "./data/weather/MPF/MPF_20250627172400.nc")),
    "var_name": _netcdf_config.get("var_name", "PRE"),
    "phase_var": _netcdf_config.get("phase_var", "phase")
}

# 行政区文件配置
_admin_config = _paths_config.get("administrative_regions", {})
ADMINISTRATIVE_REGIONS_CONFIG = {
    "city_geojson": resolve_path(_admin_config.get("city_geojson", "./data/administrative/city_regions.geojson")),
    "county_geojson": resolve_path(_admin_config.get("county_geojson", "./data/administrative/county_regions.geojson"))
}

# ================================
# 格网参数配置
# ================================

# 格网范围（云南地区）
_grid_config = _yaml_config.get("grid", {})
GRID_CONFIG = {
    "x_min": _grid_config.get("x_min", 97.50),
    "y_min": _grid_config.get("y_min", 21.10),
    "x_max": _grid_config.get("x_max", 106.25),
    "y_max": _grid_config.get("y_max", 29.30),
    "size": _grid_config.get("size", 0.01),  # 格网大小（度）
    "buffer": _grid_config.get("buffer", 0.02)  # 生成LUT时的缓冲距离（度）
}

# 标准格网编码参数
_encoding_config = _grid_config.get("encoding", {})
GRID_ENCODING = {
    "step": _encoding_config.get("step", 0.01),
    "lon_offset": _encoding_config.get("lon_offset", 180.0),
    "lat_offset": _encoding_config.get("lat_offset", 90.0)
}

# ================================
# 数据库表名配置
# ================================

# 天气相关表名
_weather_tables = _yaml_config.get("tables", {}).get("weather", {})
WEATHER_TABLES = {
    "weather_cell_6m": _weather_tables.get("weather_cell_6m", "weather_cell_6m"),
    "weather_routes": _weather_tables.get("weather_routes", "weather_routes"),
    "weather_grid": _weather_tables.get("weather_grid", "weather_grid"),
    "weather_cell_route_lut": _weather_tables.get("weather_cell_route_lut", "weather_cell_route_lut"),
    "weather_cell_route_lut_line": _weather_tables.get("weather_cell_route_lut_line", "weather_cell_route_lut_line")
}

# 预报表名
_forecast_tables = _yaml_config.get("tables", {}).get("forecast", {})
FORECAST_TABLES = {
    # 6分钟预报表
    "forecast_6min_line": _forecast_tables.get("forecast_6min_line", "forecast_precipitation_6min_line"),
    "forecast_6min_polygon": _forecast_tables.get("forecast_6min_polygon", "forecast_precipitation_6min_polygon"),
    "forecast_6min_relation": _forecast_tables.get("forecast_6min_relation", "forecast_precipitation_6min_relation"),

    # 1小时预报表
    "forecast_hourly_line": _forecast_tables.get("forecast_hourly_line", "forecast_precipitation_hourly_line"),
    "forecast_hourly_polygon": _forecast_tables.get("forecast_hourly_polygon", "forecast_precipitation_hourly_polygon"),
    "forecast_hourly_relation": _forecast_tables.get("forecast_hourly_relation", "forecast_precipitation_hourly_relation"),

    # 日预报表
    "forecast_daily_line": _forecast_tables.get("forecast_daily_line", "precipitation_daily_line"),
    "forecast_daily_polygon": _forecast_tables.get("forecast_daily_polygon", "precipitation_daily_polygon"),
    "forecast_daily_relation": _forecast_tables.get("forecast_daily_relation", "precipitation_daily_relation")
}

# ================================
# Shapefile 字段映射配置
# ================================

# Shapefile 字段名配置
_shp_fields = _yaml_config.get("shapefile", {}).get("fields", {})
SHP_FIELDS = {
    "pile_start": _shp_fields.get("pile_start", "qdzh"),  # 起点桩号（公里）
    "pile_end": _shp_fields.get("pile_end", "zdzh"),    # 终点桩号（公里）
    "owner_unit": _shp_fields.get("owner_unit", "owner_unit"),  # 所有者单位
    "route_code": _shp_fields.get("route_code", "lxbh"),  # 路线编号
    "route_name": _shp_fields.get("route_name", "lxmc"),  # 路线名称
    "maintenance_section": _shp_fields.get("maintenance_section", "maintenanc"),  # 养护路段
    "management_office": _shp_fields.get("management_office", "tbdw"),  # 管理单位
    "xzqh": _shp_fields.get("xzqh", "xzqh")  # 行政区划代码字段
}

# 行政区划代码处理配置
_xzqh_processing = _yaml_config.get("shapefile", {}).get("xzqh_processing", {})
XZQH_PROCESSING = {
    "target_digits": _xzqh_processing.get("target_digits", 6)  # 默认6位（县级）
}

# ================================
# 处理参数配置
# ================================

# 多进程配置
_processing_config = _yaml_config.get("processing", {})
PROCESSING_CONFIG = {
    "max_processes": _processing_config.get("max_processes", None),  # None表示使用 max(1, cpu_count() - 1)
    "chunk_size": _processing_config.get("chunk_size", 1),  # 每个进程一次处理的时间步数
    "snap_tolerance": _processing_config.get("snap_tolerance", 0.00001),  # 几何对齐容差
    "connection_tolerance_m": _processing_config.get("connection_tolerance_m", 10.0),  # 连接容差（米）
    "connection_tolerance_deg": _processing_config.get("connection_tolerance_deg", 1e-4)  # 连接容差（度）
}

# ================================
# 存储过程配置
# ================================

# 存储过程默认参数
_stored_proc_config = _yaml_config.get("stored_procedures", {})
STORED_PROCEDURE_CONFIG = {
    "default_calc_func": _stored_proc_config.get("default_calc_func", "get_rainfall_type"),
    "default_arg_fields": _stored_proc_config.get("default_arg_fields", ["rainfall", "phase"]),
    "default_source_table": _stored_proc_config.get("default_source_table", "weather_cell_6m"),
    "default_value_field": _stored_proc_config.get("default_value_field", "rainfall")
}

# ================================
# 天气数据下载配置
# ================================

# 天气数据下载配置
_weather_download_config = _yaml_config.get("weather_download", {})
WEATHER_DOWNLOAD_CONFIG = {
    # API配置
    "api_base_url": _weather_download_config.get("api_base_url", "http://way.weatherdt.com/apimall/basic/ncfile.htm"),
    "api_key": _weather_download_config.get("api_key", "d23bf68181a1a038a0dfb0deaa04232f"),

    # 数据类型配置
    "data_types": _weather_download_config.get("data_types", {
        "gz_mpfv3yunnan": {
            "name": "6分钟降水",
            "folder": "precipitation_6min",
            "description": "逐6分钟云南区域降水"
        },
        "gz_yunnanweather1h": {
            "name": "1小时天气现象",
            "folder": "weather_1h",
            "description": "逐小时钟云南天气现象"
        },
        "gz_yunnantem1h": {
            "name": "1小时温度",
            "folder": "temperature_1h",
            "description": "逐小时钟云南温度"
        },
        "gz_yunnanpre1h": {
            "name": "1小时降水",
            "folder": "precipitation_1h",
            "description": "逐小时钟云南降水"
        },
        "gz_yunnanvis1h": {
            "name": "1小时能见度",
            "folder": "visibility_1h",
            "description": "逐小时钟云南能见度"
        },
        "gz_obsncyunnan": {
            "name": "实况数据",
            "folder": "obs",
            "description": "云南区实况"
        }
    }),

    # 下载参数
    "download_timeout": _weather_download_config.get("download_timeout", 300),
    "max_retries": _weather_download_config.get("max_retries", 3),

    # 备份配置
    "backup_keep_days": _weather_download_config.get("backup_keep_days", 30),
    "auto_cleanup_backups": _weather_download_config.get("auto_cleanup_backups", True),

    # 功能开关
    "enable_download": _weather_download_config.get("enable_download", False),
    "enable_backup": _weather_download_config.get("enable_backup", True),

    # 默认数据类型
    "default_data_type": _weather_download_config.get("default_data_type", "gz_mpfv3")
}

# ================================
# 预警信息API配置
# ================================

# 预警信息API配置
_weather_alarm_config = _yaml_config.get("weather_alarm", {})
WEATHER_ALARM_CONFIG = {
    # API配置
    "api_base_url": _weather_alarm_config.get("api_base_url", "http://api.weatherdt.com/common/"),
    "api_key": _weather_alarm_config.get("api_key", "a49adb3feddbfe93dee18b5a64dde1f1"),
    "type": _weather_alarm_config.get("type", "alarm"),

    # 请求参数
    "max_areas_per_request": _weather_alarm_config.get("max_areas_per_request", 20),
    "request_timeout": _weather_alarm_config.get("request_timeout", 30),
    "max_retries": _weather_alarm_config.get("max_retries", 3),
    "retry_interval": _weather_alarm_config.get("retry_interval", 5),

    # 调度配置
    "schedule_interval_minutes": _weather_alarm_config.get("schedule_interval_minutes", 10)
}

# ================================
# 环境变量支持
# ================================

def load_from_env():
    """从环境变量加载配置（如果存在）"""
    # PostgreSQL 配置
    if os.getenv("POSTGRES_HOST"):
        POSTGRES_CONFIG["host"] = os.getenv("POSTGRES_HOST")
    if os.getenv("POSTGRES_PORT"):
        POSTGRES_CONFIG["port"] = int(os.getenv("POSTGRES_PORT"))
    if os.getenv("POSTGRES_DATABASE"):
        POSTGRES_CONFIG["database"] = os.getenv("POSTGRES_DATABASE")
    if os.getenv("POSTGRES_USERNAME"):
        POSTGRES_CONFIG["username"] = os.getenv("POSTGRES_USERNAME")
    if os.getenv("POSTGRES_PASSWORD"):
        POSTGRES_CONFIG["password"] = os.getenv("POSTGRES_PASSWORD")
    
    # MySQL 配置
    if os.getenv("MYSQL_HOST"):
        MYSQL_CONFIG["host"] = os.getenv("MYSQL_HOST")
    if os.getenv("MYSQL_PORT"):
        MYSQL_CONFIG["port"] = int(os.getenv("MYSQL_PORT"))
    if os.getenv("MYSQL_DATABASE"):
        MYSQL_CONFIG["database"] = os.getenv("MYSQL_DATABASE")
    if os.getenv("MYSQL_USERNAME"):
        MYSQL_CONFIG["username"] = os.getenv("MYSQL_USERNAME")
    if os.getenv("MYSQL_PASSWORD"):
        MYSQL_CONFIG["password"] = os.getenv("MYSQL_PASSWORD")

# 自动加载环境变量
load_from_env()

# ================================
# 便捷访问变量（向后兼容）
# ================================

# 数据库连接字符串
PG_URL = get_postgres_url()
MYSQL_URL = get_mysql_url()

# 数据库连接参数
DB_HOST = POSTGRES_CONFIG["host"]
DB_PORT = POSTGRES_CONFIG["port"]
DB_NAME = POSTGRES_CONFIG["database"]
DB_USER = POSTGRES_CONFIG["username"]
DB_PASS = POSTGRES_CONFIG["password"]

# MySQL 连接参数
MYSQL_HOST = MYSQL_CONFIG["host"]
MYSQL_PORT = MYSQL_CONFIG["port"]
MYSQL_USER = MYSQL_CONFIG["username"]
MYSQL_PASS = MYSQL_CONFIG["password"]
MYSQL_DB = MYSQL_CONFIG["database"]

# 格网参数
GRID_X_MIN = GRID_CONFIG["x_min"]
GRID_Y_MIN = GRID_CONFIG["y_min"]
GRID_X_MAX = GRID_CONFIG["x_max"]
GRID_Y_MAX = GRID_CONFIG["y_max"]
GRID_SIZE = GRID_CONFIG["size"]
GRID_SIZE_DEG = GRID_CONFIG["size"]
GRID_BUFFER_DEG = GRID_CONFIG["buffer"]

# NetCDF 参数
NC_PATH = NETCDF_CONFIG["default_file"]
VAR_NAME = NETCDF_CONFIG["var_name"]
PHASE_VAR = NETCDF_CONFIG["phase_var"]

# 表名
ROUTE_TB = WEATHER_TABLES["weather_routes"]
GRID_TB = WEATHER_TABLES["weather_grid"]
LUT_TB = WEATHER_TABLES["weather_cell_route_lut"]
LUT_LINE_TB = WEATHER_TABLES["weather_cell_route_lut_line"]

# Shapefile 路径和字段
SHP_FILE_PATH = DATA_PATHS["gis_route_shape"]
SHP_FIELD_PILE_START = SHP_FIELDS["pile_start"]
SHP_FIELD_PILE_END = SHP_FIELDS["pile_end"]
SHP_FIELD_OWNER_UNIT = SHP_FIELDS["owner_unit"]
SHP_FIELD_ROUTE_CODE = SHP_FIELDS["route_code"]
SHP_FIELD_ROUTE_NAME = SHP_FIELDS["route_name"]
SHP_FIELD_MAINTENANCE_SECTION = SHP_FIELDS["maintenance_section"]
SHP_FIELD_XZQH = SHP_FIELDS["xzqh"]

# 行政区划处理参数
XZQH_TARGET_DIGITS = XZQH_PROCESSING["target_digits"]

# 格网编码参数
STEP = GRID_ENCODING["step"]
LON_OFF = GRID_ENCODING["lon_offset"]
LAT_OFF = GRID_ENCODING["lat_offset"]

# 处理参数
SNAP_TOL = PROCESSING_CONFIG["snap_tolerance"]

# ================================
# 天气数据处理配置
# ================================

# 天气处理配置
WEATHER_PROCESSING_CONFIG = _yaml_config.get("weather_processing", {})

# 6分钟天气数据处理配置
WEATHER_6M_CONFIG = WEATHER_PROCESSING_CONFIG.get("weather_6m", {})
WEATHER_6M_DATA_SOURCE = WEATHER_6M_CONFIG.get("data_source", {})
WEATHER_6M_PROCESSING = WEATHER_6M_CONFIG.get("processing", {})
WEATHER_6M_DATABASE = WEATHER_6M_CONFIG.get("database", {})
WEATHER_6M_VALIDATION = WEATHER_6M_CONFIG.get("validation", {})

# 6分钟数据处理参数
WEATHER_6M_DATA_TYPE = WEATHER_6M_DATA_SOURCE.get("data_type", "gz_mpfv3")
WEATHER_6M_VARIABLE = WEATHER_6M_DATA_SOURCE.get("variable_name", "WEATHER")
WEATHER_6M_BATCH_SIZE = WEATHER_6M_PROCESSING.get("batch_size", 1000)
WEATHER_6M_MAX_CONCURRENT = WEATHER_6M_PROCESSING.get("max_concurrent_db", 20)
WEATHER_6M_TIMEOUT = WEATHER_6M_PROCESSING.get("timeout_seconds", 300)
WEATHER_6M_ENABLE_BACKUP = WEATHER_6M_PROCESSING.get("enable_backup", True)
WEATHER_6M_TARGET_TABLE = WEATHER_6M_DATABASE.get("target_table", "weather_cell_6m")
WEATHER_6M_CONFLICT_COLUMNS = WEATHER_6M_DATABASE.get("conflict_columns", ["cell_id", "timestamp"])
WEATHER_6M_STORED_PROCEDURE = WEATHER_6M_DATABASE.get("stored_procedure", "process_weather_6m_data")

# 1小时天气数据处理配置
WEATHER_1H_CONFIG = WEATHER_PROCESSING_CONFIG.get("weather_1h", {})
WEATHER_1H_DATA_SOURCE = WEATHER_1H_CONFIG.get("data_source", {})
WEATHER_1H_PROCESSING = WEATHER_1H_CONFIG.get("processing", {})
WEATHER_1H_DATABASE = WEATHER_1H_CONFIG.get("database", {})
WEATHER_1H_VALIDATION = WEATHER_1H_CONFIG.get("validation", {})

# 1小时数据处理参数
WEATHER_1H_DATA_TYPES = WEATHER_1H_DATA_SOURCE.get("data_types", {
    "PRE": "gz_didiforecast1hPRE",
    "TEM": "gz_didiforecast1hTEM",
    "WEATHER": "gz_didiforecast1hWEATHER",
    "VIS": "gz_didiforecast1hVIS"
})
WEATHER_1H_BATCH_SIZE = WEATHER_1H_PROCESSING.get("batch_size", 500)
WEATHER_1H_MAX_CONCURRENT = WEATHER_1H_PROCESSING.get("max_concurrent_db", 15)
WEATHER_1H_TIMEOUT = WEATHER_1H_PROCESSING.get("timeout_seconds", 600)
WEATHER_1H_ENABLE_BACKUP = WEATHER_1H_PROCESSING.get("enable_backup", True)
WEATHER_1H_PRIORITY_ORDER = WEATHER_1H_PROCESSING.get("priority_order", ["PRE", "TEM", "WEATHER", "VIS"])
WEATHER_1H_TARGET_TABLE = WEATHER_1H_DATABASE.get("target_table", "weather_cell_1h")
WEATHER_1H_CONFLICT_COLUMNS = WEATHER_1H_DATABASE.get("conflict_columns", ["cell_id", "timestamp"])
WEATHER_1H_STORED_PROCEDURE = WEATHER_1H_DATABASE.get("stored_procedure", "process_weather_1h_data")

# 数据验证配置
WEATHER_6M_REQUIRED_VARS = WEATHER_6M_VALIDATION.get("required_variables", ["WEATHER"])
WEATHER_6M_MIN_VALID_CELLS = WEATHER_6M_VALIDATION.get("min_valid_cells", 1000)
WEATHER_6M_VALUE_RANGE = WEATHER_6M_VALIDATION.get("value_range", [0, 100])

WEATHER_1H_REQUIRED_VARS = WEATHER_1H_VALIDATION.get("required_variables", ["PRE", "TEM"])
WEATHER_1H_OPTIONAL_VARS = WEATHER_1H_VALIDATION.get("optional_variables", ["WEATHER", "VIS"])
WEATHER_1H_MIN_VALID_CELLS = WEATHER_1H_VALIDATION.get("min_valid_cells", 1000)
WEATHER_1H_VALUE_RANGES = WEATHER_1H_VALIDATION.get("value_ranges", {
    "PRE": [0, 200],
    "TEM": [-50, 60],
    "WEATHER": [0, 100],
    "VIS": [0, 50000]
})

# ================================
# 便捷函数
# ================================

def get_weather_6m_config():
    """获取6分钟天气处理配置"""
    return {
        "data_type": WEATHER_6M_DATA_TYPE,
        "variable_name": WEATHER_6M_VARIABLE,
        "batch_size": WEATHER_6M_BATCH_SIZE,
        "max_concurrent_db": WEATHER_6M_MAX_CONCURRENT,
        "timeout_seconds": WEATHER_6M_TIMEOUT,
        "enable_backup": WEATHER_6M_ENABLE_BACKUP,
        "target_table": WEATHER_6M_TARGET_TABLE,
        "conflict_columns": WEATHER_6M_CONFLICT_COLUMNS,
        "stored_procedure": WEATHER_6M_STORED_PROCEDURE,
        "required_variables": WEATHER_6M_REQUIRED_VARS,
        "min_valid_cells": WEATHER_6M_MIN_VALID_CELLS,
        "value_range": WEATHER_6M_VALUE_RANGE
    }

def get_weather_1h_config():
    """获取1小时天气处理配置"""
    return {
        "data_types": WEATHER_1H_DATA_TYPES,
        "batch_size": WEATHER_1H_BATCH_SIZE,
        "max_concurrent_db": WEATHER_1H_MAX_CONCURRENT,
        "timeout_seconds": WEATHER_1H_TIMEOUT,
        "enable_backup": WEATHER_1H_ENABLE_BACKUP,
        "priority_order": WEATHER_1H_PRIORITY_ORDER,
        "target_table": WEATHER_1H_TARGET_TABLE,
        "conflict_columns": WEATHER_1H_CONFLICT_COLUMNS,
        "stored_procedure": WEATHER_1H_STORED_PROCEDURE,
        "required_variables": WEATHER_1H_REQUIRED_VARS,
        "optional_variables": WEATHER_1H_OPTIONAL_VARS,
        "min_valid_cells": WEATHER_1H_MIN_VALID_CELLS,
        "value_ranges": WEATHER_1H_VALUE_RANGES
    }

# ================================
# 调度器配置
# ================================

# 调度器主配置
SCHEDULER_CONFIG = _yaml_config.get("scheduler", {})

# 数据新鲜度检查配置
SCHEDULER_UPTIME_CHECK = SCHEDULER_CONFIG.get("uptime_check", {})
SCHEDULER_RETRY_INTERVAL_SECONDS = SCHEDULER_UPTIME_CHECK.get("retry_interval_seconds", 30)
SCHEDULER_MAX_RETRY_DURATION_MINUTES = SCHEDULER_UPTIME_CHECK.get("max_retry_duration_minutes", 60)
SCHEDULER_RETRY_UNTIL_FRESH = SCHEDULER_UPTIME_CHECK.get("retry_until_fresh", True)

# 任务配置
SCHEDULER_TASKS = SCHEDULER_CONFIG.get("tasks", {})

# 1小时任务配置
SCHEDULER_HOURLY_TASKS = SCHEDULER_TASKS.get("hourly", {})
SCHEDULER_HOURLY_DATA_TYPES = SCHEDULER_HOURLY_TASKS.get("data_types", ["PRE", "TEM", "WEATHER", "VIS"])
SCHEDULER_HOURLY_TIMEOUT_MINUTES = SCHEDULER_HOURLY_TASKS.get("timeout_minutes", 30)
SCHEDULER_HOURLY_SCHEDULE_PATTERN = SCHEDULER_HOURLY_TASKS.get("schedule_pattern", "0 4 * * *")

# 6分钟任务配置
SCHEDULER_SIX_MINUTE_TASKS = SCHEDULER_TASKS.get("six_minute", {})
SCHEDULER_SIX_MINUTE_DATA_TYPE = SCHEDULER_SIX_MINUTE_TASKS.get("data_type", "gz_mpfv3yunnan")
SCHEDULER_SIX_MINUTE_TIMEOUT_MINUTES = SCHEDULER_SIX_MINUTE_TASKS.get("timeout_minutes", 10)
SCHEDULER_SIX_MINUTE_SCHEDULE_PATTERN = SCHEDULER_SIX_MINUTE_TASKS.get("schedule_pattern", "4,10,16,22,28,34,40,46,52,58 * * * *")

# 实况数据任务配置
SCHEDULER_OBSERVATION_TASKS = SCHEDULER_TASKS.get("observation", {})
SCHEDULER_OBSERVATION_DATA_TYPE = SCHEDULER_OBSERVATION_TASKS.get("data_type", "gz_obsncyunnan")
SCHEDULER_OBSERVATION_TIMEOUT_MINUTES = SCHEDULER_OBSERVATION_TASKS.get("timeout_minutes", 15)
SCHEDULER_OBSERVATION_SCHEDULE_PATTERN = SCHEDULER_OBSERVATION_TASKS.get("schedule_pattern", "8,18,28,38,48,58 * * * *")

# 分区维护任务配置
SCHEDULER_PARTITION_MAINTENANCE_TASKS = SCHEDULER_TASKS.get("partition_maintenance", {})
SCHEDULER_PARTITION_MAINTENANCE_TIMEOUT_MINUTES = SCHEDULER_PARTITION_MAINTENANCE_TASKS.get("timeout_minutes", 10)
SCHEDULER_PARTITION_MAINTENANCE_SCHEDULE_PATTERN = SCHEDULER_PARTITION_MAINTENANCE_TASKS.get("schedule_pattern", "0 2 * * *")

# 资源池配置
SCHEDULER_RESOURCES = SCHEDULER_CONFIG.get("resources", {})

# 数据库资源配置
SCHEDULER_DB_RESOURCES = SCHEDULER_RESOURCES.get("database", {})
SCHEDULER_DB_MAX_CONNECTIONS = SCHEDULER_DB_RESOURCES.get("max_connections", 20)
SCHEDULER_DB_MIN_CONNECTIONS = SCHEDULER_DB_RESOURCES.get("min_connections", 5)

# 线程池配置
SCHEDULER_THREAD_POOL = SCHEDULER_RESOURCES.get("thread_pool", {})
SCHEDULER_THREAD_MAX_WORKERS = SCHEDULER_THREAD_POOL.get("max_workers", 32)

# 进程池配置
SCHEDULER_PROCESS_POOL = SCHEDULER_RESOURCES.get("process_pool", {})
SCHEDULER_PROCESS_MAX_WORKERS = SCHEDULER_PROCESS_POOL.get("max_workers", None)

# 监控配置
SCHEDULER_MONITORING = SCHEDULER_CONFIG.get("monitoring", {})
SCHEDULER_LOG_LEVEL = SCHEDULER_MONITORING.get("log_level", "INFO")
SCHEDULER_LOG_FILE = SCHEDULER_MONITORING.get("log_file", "weather_scheduler.log")
SCHEDULER_HEALTH_CHECK_INTERVAL = SCHEDULER_MONITORING.get("health_check_interval", 30)

# Web服务配置
SCHEDULER_WEB = SCHEDULER_CONFIG.get("web", {})
SCHEDULER_WEB_HOST = SCHEDULER_WEB.get("host", "0.0.0.0")
SCHEDULER_WEB_PORT = SCHEDULER_WEB.get("port", 8000)
SCHEDULER_WEB_RELOAD = SCHEDULER_WEB.get("reload", False)


def get_scheduler_config():
    """获取调度器完整配置"""
    return {
        "uptime_check": {
            "retry_interval_seconds": SCHEDULER_RETRY_INTERVAL_SECONDS,
            "max_retry_duration_minutes": SCHEDULER_MAX_RETRY_DURATION_MINUTES,
            "retry_until_fresh": SCHEDULER_RETRY_UNTIL_FRESH
        },
        "tasks": {
            "hourly": {
                "data_types": SCHEDULER_HOURLY_DATA_TYPES,
                "timeout_minutes": SCHEDULER_HOURLY_TIMEOUT_MINUTES,
                "schedule_pattern": SCHEDULER_HOURLY_SCHEDULE_PATTERN
            },
            "six_minute": {
                "data_type": SCHEDULER_SIX_MINUTE_DATA_TYPE,
                "timeout_minutes": SCHEDULER_SIX_MINUTE_TIMEOUT_MINUTES,
                "schedule_pattern": SCHEDULER_SIX_MINUTE_SCHEDULE_PATTERN
            },
            "observation": {
                "data_type": SCHEDULER_OBSERVATION_DATA_TYPE,
                "timeout_minutes": SCHEDULER_OBSERVATION_TIMEOUT_MINUTES,
                "schedule_pattern": SCHEDULER_OBSERVATION_SCHEDULE_PATTERN
            }
        },
        "resources": {
            "database": {
                "max_connections": SCHEDULER_DB_MAX_CONNECTIONS,
                "min_connections": SCHEDULER_DB_MIN_CONNECTIONS
            },
            "thread_pool": {
                "max_workers": SCHEDULER_THREAD_MAX_WORKERS
            },
            "process_pool": {
                "max_workers": SCHEDULER_PROCESS_MAX_WORKERS
            }
        },
        "monitoring": {
            "log_level": SCHEDULER_LOG_LEVEL,
            "log_file": SCHEDULER_LOG_FILE,
            "health_check_interval": SCHEDULER_HEALTH_CHECK_INTERVAL
        },
        "web": {
            "host": SCHEDULER_WEB_HOST,
            "port": SCHEDULER_WEB_PORT,
            "reload": SCHEDULER_WEB_RELOAD
        }
    }
