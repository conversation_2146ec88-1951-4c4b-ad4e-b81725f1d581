#!/usr/bin/env python3
"""
手动清理旧的下载文件和备份文件

这个脚本可以立即清理所有数据类型的旧文件，只保留指定天数的数据。
默认只保留1天的数据，可以通过命令行参数修改。

使用方法:
    python cleanup_old_files.py                    # 只保留1天数据
    python cleanup_old_files.py --keep-days 2      # 保留2天数据
    python cleanup_old_files.py --dry-run          # 试运行，不实际删除文件
    python cleanup_old_files.py --backup-only      # 只清理备份文件
    python cleanup_old_files.py --downloads-only   # 只清理下载文件
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from weather_download import WeatherDownloader
from config import WEATHER_DOWNLOAD_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def cleanup_files(keep_days=1, dry_run=False, backup_only=False, downloads_only=False):
    """
    清理旧文件
    
    Args:
        keep_days: 保留天数
        dry_run: 是否为试运行（不实际删除文件）
        backup_only: 是否只清理备份文件
        downloads_only: 是否只清理下载文件
    """
    logger.info(f"开始清理文件，保留 {keep_days} 天的数据")
    if dry_run:
        logger.info("*** 试运行模式，不会实际删除文件 ***")
    
    # 获取所有数据类型
    data_types = list(WEATHER_DOWNLOAD_CONFIG.get("data_types", {}).keys())
    logger.info(f"发现 {len(data_types)} 种数据类型: {', '.join(data_types)}")
    
    total_cleaned_types = 0
    
    for data_type in data_types:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"开始清理数据类型: {data_type}")
            logger.info(f"{'='*60}")
            
            downloader = WeatherDownloader(data_type)
            
            # 清理下载文件
            if not backup_only:
                logger.info(f"清理 {data_type} 的下载文件...")
                if not dry_run:
                    downloader.cleanup_old_downloads(keep_days=keep_days)
                else:
                    logger.info(f"[试运行] 将清理 {data_type} 超过 {keep_days} 天的下载文件")
            
            # 清理备份文件
            if not downloads_only:
                logger.info(f"清理 {data_type} 的备份文件...")
                if not dry_run:
                    downloader.cleanup_old_backups(keep_days=keep_days)
                else:
                    logger.info(f"[试运行] 将清理 {data_type} 超过 {keep_days} 天的备份文件")
            
            logger.info(f"✓ 数据类型 {data_type} 清理完成")
            total_cleaned_types += 1
            
        except Exception as e:
            logger.error(f"✗ 清理数据类型 {data_type} 时发生错误: {e}")
            continue
    
    logger.info(f"\n{'='*60}")
    logger.info(f"文件清理完成！")
    logger.info(f"成功清理了 {total_cleaned_types}/{len(data_types)} 种数据类型")
    logger.info(f"保留天数: {keep_days} 天")
    if dry_run:
        logger.info("*** 这是试运行，没有实际删除文件 ***")
    logger.info(f"{'='*60}")


def get_disk_usage_info():
    """获取磁盘使用情况"""
    try:
        import shutil
        from config import WEATHER_DOWNLOAD_CONFIG
        
        logger.info("\n磁盘使用情况:")
        logger.info("-" * 40)
        
        # 检查每种数据类型的目录大小
        data_types = WEATHER_DOWNLOAD_CONFIG.get("data_types", {})
        
        for data_type, config in data_types.items():
            try:
                downloader = WeatherDownloader(data_type)
                base_dir = downloader.base_dir
                backup_dir = downloader.backup_dir
                
                if base_dir.exists():
                    base_size = sum(f.stat().st_size for f in base_dir.rglob('*') if f.is_file())
                    base_size_mb = base_size / (1024 * 1024)
                    logger.info(f"{data_type} 数据目录: {base_size_mb:.1f} MB")
                
                if backup_dir.exists():
                    backup_size = sum(f.stat().st_size for f in backup_dir.rglob('*') if f.is_file())
                    backup_size_mb = backup_size / (1024 * 1024)
                    logger.info(f"{data_type} 备份目录: {backup_size_mb:.1f} MB")
                    
            except Exception as e:
                logger.error(f"获取 {data_type} 目录大小失败: {e}")
        
        # 获取总磁盘使用情况
        total, used, free = shutil.disk_usage("/")
        total_gb = total / (1024**3)
        used_gb = used / (1024**3)
        free_gb = free / (1024**3)
        usage_percent = (used / total) * 100
        
        logger.info("-" * 40)
        logger.info(f"磁盘总容量: {total_gb:.1f} GB")
        logger.info(f"已使用: {used_gb:.1f} GB ({usage_percent:.1f}%)")
        logger.info(f"可用空间: {free_gb:.1f} GB")
        
    except Exception as e:
        logger.error(f"获取磁盘使用情况失败: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="清理旧的天气数据文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                        # 只保留1天数据
  %(prog)s --keep-days 2          # 保留2天数据
  %(prog)s --dry-run              # 试运行，查看将要删除的文件
  %(prog)s --backup-only          # 只清理备份文件
  %(prog)s --downloads-only       # 只清理下载文件
  %(prog)s --disk-info            # 查看磁盘使用情况
        """
    )
    
    parser.add_argument(
        "--keep-days",
        type=int,
        default=1,
        help="保留天数 (默认: 1天)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="试运行模式，不实际删除文件"
    )
    
    parser.add_argument(
        "--backup-only",
        action="store_true",
        help="只清理备份文件"
    )
    
    parser.add_argument(
        "--downloads-only",
        action="store_true",
        help="只清理下载文件"
    )
    
    parser.add_argument(
        "--disk-info",
        action="store_true",
        help="显示磁盘使用情况"
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if args.backup_only and args.downloads_only:
        logger.error("--backup-only 和 --downloads-only 不能同时使用")
        sys.exit(1)
    
    if args.keep_days < 0:
        logger.error("保留天数不能为负数")
        sys.exit(1)
    
    # 显示磁盘使用情况
    if args.disk_info:
        get_disk_usage_info()
        if not any([args.dry_run, args.backup_only, args.downloads_only]) and args.keep_days == 1:
            return  # 如果只是查看磁盘信息，不执行清理
    
    # 执行清理
    cleanup_files(
        keep_days=args.keep_days,
        dry_run=args.dry_run,
        backup_only=args.backup_only,
        downloads_only=args.downloads_only
    )


if __name__ == "__main__":
    main()
